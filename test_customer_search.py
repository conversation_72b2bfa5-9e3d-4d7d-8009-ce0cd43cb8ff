#!/usr/bin/env python3
"""
测试客户搜索功能的修复效果
"""

import requests
import json

def test_backend_api():
    """测试后端API"""
    base_url = "http://localhost:8403"
    
    print("=== 测试后端客户搜索API ===")
    
    # 1. 测试获取所有客户
    print("\n1. 获取所有客户:")
    try:
        response = requests.get(f"{base_url}/api/admin/customers")
        if response.status_code == 200:
            customers = response.json()
            print(f"   总共 {len(customers)} 个客户")
            if customers:
                print(f"   示例: {customers[0]['account']} ({customers[0]['nickname']})")
        else:
            print(f"   错误: {response.status_code}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 2. 测试搜索功能
    print("\n2. 搜索包含'a'的客户:")
    try:
        response = requests.get(f"{base_url}/api/admin/customers", params={
            "search": "a",
            "limit": "10"
        })
        if response.status_code == 200:
            customers = response.json()
            print(f"   找到 {len(customers)} 个客户")
            for customer in customers[:3]:  # 显示前3个
                print(f"   - {customer['account']} ({customer['nickname']})")
        else:
            print(f"   错误: {response.status_code}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 3. 测试精确搜索
    print("\n3. 搜索'aaron':")
    try:
        response = requests.get(f"{base_url}/api/admin/customers", params={
            "search": "aaron"
        })
        if response.status_code == 200:
            customers = response.json()
            print(f"   找到 {len(customers)} 个客户")
            for customer in customers:
                print(f"   - {customer['account']} ({customer['nickname']})")
        else:
            print(f"   错误: {response.status_code}")
    except Exception as e:
        print(f"   异常: {e}")

def test_resource_whitelist_api():
    """测试资源白名单搜索API"""
    base_url = "http://localhost:8403"
    
    print("\n=== 测试资源白名单客户搜索API ===")
    
    # 测试客户搜索
    print("\n1. 搜索客户'a':")
    try:
        response = requests.get(f"{base_url}/api/admin/resource-whitelist/search", params={
            "customer": "a"
        })
        if response.status_code == 200:
            data = response.json()
            customers = data.get("customers", [])
            print(f"   找到 {len(customers)} 个客户")
            for customer in customers[:3]:  # 显示前3个
                print(f"   - {customer['account']} ({customer['nickname']})")
        else:
            print(f"   错误: {response.status_code}")
    except Exception as e:
        print(f"   异常: {e}")

def main():
    """主函数"""
    print("客户搜索功能测试")
    print("=" * 50)
    
    test_backend_api()
    test_resource_whitelist_api()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
