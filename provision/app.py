"""Main module."""

from pathlib import Path

from blacksheep import Application, Request, json
from loguru import logger

from provision.common.router import register_routes, router
from provision.common.sessions import sessions
from provision.conf import cfg
from provision.conf.plans import sp
from provision.service.resources import rm
from provision.store.db import pg
from provision.tasks.scheduler import init_scheduler, start_scheduler, stop_scheduler

app = Application(router=router, show_error_details=True)
app.use_cors(allow_methods="*", allow_origins="*", allow_headers="*")


def configure_logging():
    """配置应用日志系统，根据配置文件设置日志处理器"""
    logger.remove()
    handlers = cfg.provision.server.logging.handlers
    for handler in handlers:
        sink = handler.sink
        handler_dict = handler.model_dump()
        del handler_dict["sink"]
        logger.add(sink, **handler_dict)


def fallback(request: Request):
    """默认的fallback函数，当没有匹配到路由时返回"""
    logger.warning(f"No route found for {request.path}")
    return json(
        {"status": "error", "message": f"No route handler for {request.path}"},
        status=404,
    )


# @app.router.get("/academy")
# async def serve_academy(request: Request):
#     """处理/academy路径的请求，根据用户登录状态重定向到相应页面"""
#     # 检查用户是否已登录
#     token = request.cookies.get("qsession-token")
#     if token is None:
#         return redirect("/academy/login")

#     try:
#         data = jwt.decode(token, cfg.provision.server.secret, algorithms=["HS256"])
#         customer_id = data.get("account_id")

#         if not customer_id or not sessions.is_active(customer_id):
#             return redirect("/academy/login")

#         # 已登录，返回成功状态码
#         return Response(status=200)
#     except jwt.InvalidTokenError:
#         logger.warning("Invalid token detected")
#         return redirect("/academy/login")
#     except KeyError as e:
#         logger.warning(f"Missing key in token: {e}")
#         return redirect("/academy/login")


# @app.router.get("/course")
# async def serve_course(request: Request):
#     """将/course路径重定向到/academy路径"""
#     # 重用academy的路由逻辑
#     return await serve_academy(request)


# 应用启动事件
@app.on_start
async def app_start(_):
    """应用启动时执行的操作"""
    cfg.load_config()
    configure_logging()

    logger.info("应用正在启动...")
    sessions.load_sessions()
    register_routes()

    # 数据库初始化
    logger.info("正在初始化数据库...")
    pg.connect(cfg)
    pg.init_db()
    logger.info("数据库初始化完成")

    # 初始化默认价格档位
    from provision.store.price_tier import PriceTierModel
    PriceTierModel.init_default_tiers()

    app.router.fallback = fallback
    logger.info(app.router.routes)

    # 静态文件目录
    static_path = Path(__file__).parent / "static"

    # 为 admin SPA 配置静态文件服务，使用 fallback_document 处理 HTML5 History API
    admin_path = static_path / "admin"
    app.serve_files(admin_path, root_path="admin", fallback_document="index.html")

    # 为 academy SPA 配置静态文件服务，使用 fallback_document 处理 HTML5 History API
    academy_path = static_path / "academy"
    app.serve_files(academy_path, root_path="academy", fallback_document="index.html")

    # 为其他静态文件配置服务（不包含 admin 和 academy）
    app.serve_files(static_path, root_path="", index_document="index.html")

    # 初始化资源管理器
    logger.info("正在初始化资源管理器...")
    sp.load_config()
    rm.on_start()

    # 初始化并启动资源访问更新调度器
    init_scheduler()
    start_scheduler()
    logger.info("应用程序成功启动。")


# 应用停止事件
@app.on_stop
async def app_stop(_):
    """应用停止时执行的操作"""
    stop_scheduler()

    pg.close()
    logger.info("应用已安全关闭")
