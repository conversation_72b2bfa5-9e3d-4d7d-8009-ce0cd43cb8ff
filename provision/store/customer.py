import datetime
import hashlib
import re
from secrets import token_hex
from typing import Optional, <PERSON><PERSON>

from loguru import logger
from pydantic import BaseModel, Field, field_validator
from typing_extensions import Self

from provision.store.db import pg


class CustomerModel(BaseModel):
    """
    客户信息模型

    Attributes:
        nickname: 客户昵称
        account: 客户账号，只能使用英文字母和数字
        phone: 手机号码
        occupation: 职业信息
        register_time: 注册时间，默认为当前时间
    """

    nickname: str
    account: str
    # 更新用户时，我们不会更新密码，所以，传入项为None
    password: str | None = Field(default=None, exclude=True)
    phone: str
    wx: str
    channel: str | None = None
    occupation: str | None = None
    register_time: datetime.datetime = Field(default_factory=datetime.datetime.now)

    # 服务端生成字段
    id: int | None = None
    salt: str | None = None

    @classmethod
    @field_validator("account")
    def validate_account(cls, value):
        """验证账号是否只包含英文字母和数字"""
        if not re.match(r"^[a-zA-Z0-9]+$", value):
            raise ValueError("Account must contain only letters and numbers")
        return value

    @classmethod
    @field_validator("phone")
    def validate_phone(cls, value):
        """验证手机号格式"""
        # 简单的手机号验证，可以根据需要调整
        if not re.match(r"^\d{11}$", value):
            raise ValueError("Phone number must be 11 digits")
        return value

    @classmethod
    def find_customer(cls, customer_id: int)->Self|None:
        """返回customer对象"""
        sql = "SELECT * FROM customers WHERE id = %s"
        result = pg.fetchone(cls, sql, [customer_id])
        
        return result

    @classmethod
    def find_customer_by_account(cls, account: str) -> Optional[Self]:
        sql = "SELECT * FROM customers WHERE account = %s"
        return pg.fetchone(cls, sql, [account])

    @classmethod
    def generate_salt(cls) -> str:
        """生成16字节的随机盐值"""
        return token_hex(16)

    @classmethod
    def hash_password(cls, password: str, salt: str | None = None) -> Tuple[str, str]:
        """生成密码哈希"""

        if salt is None:
            salt = cls.generate_salt()

        password = hashlib.pbkdf2_hmac(
            "sha256", password.encode("utf-8"), salt.encode("utf-8"), 100000
        ).hex()

        return password, salt

    def insert_customer(self):
        """插入客户信息"""
        sql = """
            INSERT INTO customers (nickname, account, phone, wx, occupation, channel, salt, password)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        """
        rowid = pg.fetch_item(sql, "id", [
                self.nickname,
                self.account,
                self.phone,
                self.wx,
                self.occupation,
                self.channel,
                self.salt,
                self.password,
            ],readonly=False
        )
        if rowid is None:
            logger.warning("Insert {} {} failed, no customer_id returned", self.nickname, self.account)
            raise ValueError(f"Insert {self.nickname} {self.account} failed, no customer_id returned")

        self.id = rowid
        logger.info(f"New customer registered: {self.account}[{self.id}]")
        return {"data": {"customer_id": self.id}}

    @classmethod
    def find_all(
        cls, page: int = 1, page_size: int = 100
    ) -> list[Self]:
        """获取所有客户列表，返回客户模型列表"""
        offset = (page - 1) * page_size
        sql = "SELECT * FROM customers LIMIT %s OFFSET %s"

        try:
            return pg.fetchall(cls, sql, [page_size, offset]) or []
        except Exception as e:
            logger.error(f"Error fetching all customers: {e}")
            raise

    @classmethod
    def find_all_customers(cls) -> list[Self]:
        """获取所有客户列表，返回字典列表"""
        try:
            sql = "SELECT * FROM customers"
            return pg.fetchall(cls, sql) or []
        except Exception as e:
            logger.error(f"Error fetching all customers as dict: {e}")
            return []

    def update_customer(self, customerId: int, save_password: bool = False):
        """更新客户信息"""
        if save_password:
            sql = """
                UPDATE customers
                SET account = %s, nickname = %s, phone = %s, wx = %s, channel = %s, password = %s, salt = %s
                WHERE id = %s
            """
            return pg.execute(
                sql,
                [
                    self.account,
                    self.nickname,
                    self.phone,
                    self.wx,
                    self.channel,
                    self.password,
                    self.salt,
                    customerId,
                ],
            )

        sql = """
            UPDATE customers
            SET account = %s, nickname = %s, phone = %s, wx = %s, channel = %s
            WHERE id = %s
        """
        return pg.execute(
            sql,
            [
                self.account,
                self.nickname,
                self.phone,
                self.wx,
                self.channel,
                customerId,
            ],
        )

    def update_password(self, new_password: str) -> bool:
        """更新用户密码

        Args:
            new_password: 新密码

        Returns:
            bool: 更新是否成功
        """
        try:
            hash_password, salt = CustomerModel.hash_password(new_password)

            # 更新数据库
            sql = """
                UPDATE customers
                SET password = %s, salt = %s
                WHERE id = %s
            """
            pg.execute(sql, [hash_password, salt, self.id])
            # 更新当前对象的密码
            self.password = hash_password

            return True
        except Exception as e:
            logger.error(f"Error updating password: {e}")
            return False

    @classmethod
    def find_by_keyword(
        cls, keyword: str, page: int = 1, page_size: int = 20
    ) -> list[Self]:
        """根据关键字搜索客户

        搜索字段包括：account, nickname, phone, wx

        Args:
            keyword: 搜索关键字
            page: 页码
            page_size: 每页数量

        Returns:
            list[CustomerModel]: 客户模型列表
        """
        offset = (page - 1) * page_size
        try:
            # 构建模糊搜索条件
            search_term = f"%{keyword}%"

            sql = """
                SELECT * FROM customers
                WHERE account LIKE %s OR nickname LIKE %s OR phone LIKE %s OR wx LIKE %s
                LIMIT %s OFFSET %s
                """

            return pg.fetchall(cls, sql,
                [search_term, search_term, search_term, search_term, page_size, offset],
            ) or []

        except Exception as e:
            logger.error(f"Error searching customers by keyword: {e}")
            return []

    @classmethod
    def search_by_keyword(
        cls, keyword: str, limit: int = 0
    ) -> list[Self]:
        """根据关键字搜索客户（无分页限制）

        搜索字段包括：account, nickname, phone, wx

        Args:
            keyword: 搜索关键字
            limit: 结果数量限制，0表示无限制

        Returns:
            list[CustomerModel]: 客户模型列表
        """
        try:
            # 构建模糊搜索条件
            search_term = f"%{keyword}%"

            sql = """
                SELECT * FROM customers
                WHERE account LIKE %s OR nickname LIKE %s OR phone LIKE %s OR wx LIKE %s
                ORDER BY id
                """

            params = [search_term, search_term, search_term, search_term]

            if limit > 0:
                sql += f" LIMIT {limit}"

            return pg.fetchall(cls, sql, params) or []

        except Exception as e:
            logger.error(f"Error searching customers by keyword: {e}")
            return []

    @classmethod
    def check_account_exists(cls, account: str) -> bool:
        """检查账号是否已存在

        Args:
            account: 账号

        Returns:
            bool: 账号是否存在
        """
        sql = "SELECT COUNT(*) FROM customers WHERE account = %s"

        # count clause应该永远能返回值。
        count = pg.fetch_item(sql, "count", [account]) or 0
        return count > 0

    @classmethod
    def check_phone_exists(cls, phone: str) -> bool:
        """检查手机号是否已存在

        Args:
            phone: 手机号

        Returns:
            bool: 手机号是否存在
        """
        if not phone:
            return False

        sql = "SELECT COUNT(*) FROM customers WHERE phone = %s"
        count = pg.fetch_item(sql, "count", [phone]) or 0
        return count > 0

    @classmethod
    def deactivate_account(cls, account: str) -> bool:
        """注销客户账号（软删除）

        Args:
            account: 账号

        Returns:
            bool: 是否成功注销
        """
        try:
            sql = "SELECT COUNT(*) FROM customers WHERE account = %s AND is_active = TRUE"
            result = pg.fetch_record(sql, [account])
            account_check = result["count"] if result else 0

            if account_check == 0:
                return False

            # 软删除客户账号
            sql = "UPDATE customers SET is_active = FALSE WHERE account = %s"
            pg.execute(sql, [account])

            return True
        except Exception as e:
            logger.error(f"Error deactivating account {account}: {e}")
            return False
