from typing import Any, List, Type, TypeVar

from loguru import logger
from psycopg2 import pool
from psycopg2.extras import RealDictCursor

T = TypeVar('T')

class Postgres:
    conn_pool: pool.ThreadedConnectionPool
    def connect(self, cfg):
        if hasattr(self, "conn_pool"):
            logger.info("Postgres connection pool already exists")
            return

        self.conn_pool =  pool.ThreadedConnectionPool(
            minconn=cfg.provision.postgres.pool.min_size or 1,
            maxconn=cfg.provision.postgres.pool.max_size or 10,
            host=cfg.provision.postgres.host or 'localhost',
            port=cfg.provision.postgres.port or 5432,
            dbname=cfg.provision.postgres.database or 'provision',
            user=cfg.provision.postgres.user,
            password=cfg.provision.postgres.password,
        )
        logger.info("connected to postgres with %s", cfg.provision.postgres)

    def is_column_exists(self, table_name: str, column_name: str) -> bool:
        """判断字段是否存在"""
        query = """
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = %s AND column_name = %s
        """
        return self.fetch_item(query, "column_name", [table_name, column_name]) is not None

    def get_column_datatype(self, table_name: str, column_name: str) -> str:
        """获取字段的数据类型"""
        query = """
            SELECT data_type
            FROM information_schema.columns
            WHERE table_name = %s AND column_name = %s
        """
        result = self.fetch_record(query, [table_name, column_name])
        if result is None:
            raise ValueError(f"{column_name}在{table_name}中不存在")

        return result[0]

    def rename_column(self, table_name: str, src: str, dst: str):
        """重命名表格字段，用于migration。"""
        if self.is_column_exists(table_name, dst):
            return

        if not self.is_column_exists(table_name, src):
            raise ValueError(f"{src} 在表格 {table_name} 中不存在")

        sql = f"ALTER TABLE {table_name} RENAME COLUMN {src} TO {dst}"
        self.execute(sql)

    def add_column_if_not_exists(
        self,
        table_name: str,
        column_name: str,
        data_type: str,
        default_value: Any | None = None,
    ):
        """
        如果指定的列不存在于表中，则添加该列

        Args:
            table_name: 表名
            column_name: 列名
            data_type: 数据类型(如 "VARCHAR", "INTEGER")
            default_value: 默认值（如果是字符串，需用引号包裹）
        """
        if not self.is_column_exists(table_name, column_name):
            # 列不存在，执行添加列操作
            default_sql = (
                f"DEFAULT {default_value}" if default_value is not None else ""
            )

            sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {data_type} {default_sql}"
            self.execute(sql)

    def init_db(self):
        """初始化数据库表"""
        sqls = [
            """
                CREATE TABLE IF NOT EXISTS customers (
                    id SERIAL PRIMARY KEY,
                    account VARCHAR NOT NULL UNIQUE,
                    password VARCHAR NOT NULL,
                    nickname VARCHAR NOT NULL,
                    salt VARCHAR NOT NULL,
                    phone VARCHAR,
                    wx VARCHAR,
                    occupation VARCHAR,
                    channel VARCHAR,
                    register_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                );
            """,
            """
                CREATE TABLE IF NOT EXISTS registry (
                    id SERIAL PRIMARY KEY,
                    course VARCHAR NOT NULL,
                    customer_id INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
                    plan VARCHAR NOT NULL,
                    start_time TIMESTAMP NOT NULL,
                    refund_end TIMESTAMP NOT NULL,
                    prime_end TIMESTAMP NOT NULL,
                    retention_end TIMESTAMP NOT NULL,
                    expire_time TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    division VARCHAR NOT NULL
                );
            """,
            """
                CREATE TABLE IF NOT EXISTS messages (
                    id SERIAL PRIMARY KEY,
                    title VARCHAR NOT NULL,
                    content TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    sender_id INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
                    recipient_id INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
                    is_read BOOLEAN DEFAULT FALSE,
                    is_deleted BOOLEAN DEFAULT FALSE
                );
            """,
            """
                CREATE TABLE IF NOT EXISTS resources (
                    id SERIAL PRIMARY KEY,
                    course VARCHAR NOT NULL,
                    resource VARCHAR NOT NULL,
                    seq INTEGER NOT NULL,
                    title VARCHAR NOT NULL,
                    description TEXT,
                    rel_path VARCHAR NOT NULL,
                    publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    price FLOAT DEFAULT 0.0,
                    division VARCHAR NOT NULL,
                    img VARCHAR,
                    UNIQUE (course, rel_path)
                );
            """,
            """
                CREATE TABLE IF NOT EXISTS resource_whitelist (
                    id SERIAL PRIMARY KEY,
                    customer_id INTEGER NOT NULL REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
                    resource_id INTEGER NOT NULL REFERENCES resources(id) ON UPDATE CASCADE ON DELETE CASCADE,
                    course VARCHAR NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE (customer_id, resource_id)
                );

                -- 创建公开资源白名单表
                CREATE TABLE IF NOT EXISTS whitelist_for_all (
                    id SERIAL PRIMARY KEY,
                    resource_id INTEGER NOT NULL REFERENCES resources(id) ON UPDATE CASCADE ON DELETE CASCADE,
                    course VARCHAR NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE (resource_id)
                );

                -- 抽奖奖券表
                CREATE TABLE IF NOT EXISTS lottery_tickets (
                    id SERIAL PRIMARY KEY,
                    ticket_code VARCHAR(5) NOT NULL,
                    customer_id INTEGER NOT NULL REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
                    course VARCHAR NOT NULL,
                    discount INTEGER NOT NULL,
                    base_fare INTEGER NOT NULL,
                    max_discount INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    status VARCHAR NOT NULL DEFAULT 'unused',
                    transferred_to INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE SET NULL,
                    transferred_at TIMESTAMP,
                    used_at TIMESTAMP,
                    order_id VARCHAR,
                    UNIQUE (ticket_code)
                );

                -- 抽奖日志表
                CREATE TABLE IF NOT EXISTS lottery_logs (
                    id SERIAL PRIMARY KEY,
                    customer_id INTEGER NOT NULL REFERENCES customers(id) ON UPDATE CASCADE ON DELETE CASCADE,
                    course VARCHAR NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    result VARCHAR NOT NULL
                );

                -- 访问记录表
                CREATE TABLE IF NOT EXISTS visits (
                    id SERIAL PRIMARY KEY,
                    customer_id INTEGER REFERENCES customers(id) ON UPDATE CASCADE ON DELETE SET NULL,
                    ip_address VARCHAR NOT NULL,
                    user_agent TEXT,
                    device_info TEXT,
                    url_path VARCHAR,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );

                -- 创建访问记录表的索引
                CREATE INDEX IF NOT EXISTS idx_visits_customer_id ON visits (customer_id);
                CREATE INDEX IF NOT EXISTS idx_visits_ip_address ON visits (ip_address);
                CREATE INDEX IF NOT EXISTS idx_visits_timestamp ON visits (timestamp);

                -- 价格档位表
                CREATE TABLE IF NOT EXISTS price_tiers (
                    id SERIAL PRIMARY KEY,
                    price DECIMAL(10,2) NOT NULL UNIQUE,
                    wechat_url VARCHAR,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """,
        ]

        conn = self.conn_pool.getconn()
        try:
            with conn:
                with conn.cursor() as cursor:
                    for sql in sqls:
                        cursor.execute(sql)
            conn.commit()

            # 添加新字段（如果不存在）
            self.add_column_if_not_exists(
                "resources",
                "price_tier_id",
                "INTEGER REFERENCES price_tiers(id) ON UPDATE CASCADE ON DELETE SET NULL"
            )

        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            conn.rollback()
        finally:
            self.conn_pool.putconn(conn)

    def _execute(self, query: str, args=None, return_dict = False):
        """返回 (cursor, conn) 元组，确保异常安全。

        Notice:
            供内部使用。调用者必须执行cursor关闭，归还conn。
        """
        conn = self.conn_pool.getconn()
        cursor = None
        try:
            cursor_factory = RealDictCursor if return_dict else None
            cursor = conn.cursor(cursor_factory = cursor_factory)

            logger.debug("Executing SQL: {}", query)
            cursor.execute(query, args)
            return cursor, conn
        except Exception as e:
            if cursor:
                cursor.close()
            self.conn_pool.putconn(conn)
            logger.error(f"SQL execution error: {e}")
            raise

    def execute(self, query: str, args=None)->None:
        """执行SQL语句，确保资源安全，自动提交，不返回结果"""
        cursor, conn = None, None
        try:
            cursor, conn = self._execute(query, args)
            conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"SQL execution error: {e}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                self.conn_pool.putconn(conn)

    def fetchone(self, model: Type[T], query: str, args: List[Any]|None=None) -> T | None:
        """执行查询语句，并返回第一个记录并转换为模型，确保资源安全。

        Notice: 本方法不会进行提交。所以，只能执行查询语句。
        """
        cursor, conn = None, None
        try:
            cursor, conn = self._execute(query, args, return_dict = True)
            result = cursor.fetchone()
            if not result:
                return None
            return model(**result)
        except Exception as e:
            logger.error(f"Fetchone error: {e}")
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                self.conn_pool.putconn(conn)

    def fetchall(self, model: Type[T], query: str, args: List[Any]|None=None) -> List[T]|None:
        """查询并返回所有记录，已转换为Model
        Notice: 本方法不会提交，所以，只能执行查询语句。
        """
        cursor, conn = None, None
        try:
            cursor, conn = self._execute(query, args, return_dict = True)
            results = cursor.fetchall()
            if not results:
                return None
            return [model(**result) for result in results]
        except Exception as e:
            logger.error(f"Fetchall error: {e}")
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                self.conn_pool.putconn(conn)

    def fetch_record(self, query: str, args: List[Any]|None=None, readonly = True, return_dict = True)->Any|None:
        """查询并返回第一条记录

        Notice: 本方法也可以执行需要返回结果的修改类操作，此时一定要设置 readonly = False,否则修改不会生效。
        """""
        cursor, conn = None, None
        try:
            cursor, conn = self._execute(query, args, return_dict)
            if not readonly:
                conn.commit()
            return cursor.fetchone()
        except Exception as e:
            logger.error(f"Fetch record error: {e}")
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                self.conn_pool.putconn(conn)

    def fetch_records(self, query: str, args: List[Any]|None=None, readonly = True,  return_dict = True)->List[Any]|None:
        """查询并返回所有记录

        Notice: 本方法也可以执行需要返回结果的修改类操作，此时一定要设置 readonly = False,否则修改不会生效。
        """
        cursor, conn = None, None
        try:
            cursor, conn = self._execute(query, args, return_dict)
            if not readonly:
                conn.commit()
            return cursor.fetchall()
        except Exception as e:
            logger.error(f"Fetch records error: {e}")
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                self.conn_pool.putconn(conn)

    def fetch_item(self, query: str, column: str, args: List[Any]|None = None, readonly=True) -> Any | None:
        """查询并返回指定列的第一个值"""
        cursor, conn = None, None
        try:
            cursor, conn = self._execute(query, args, return_dict=True)
            if not readonly:
                conn.commit()

            result = cursor.fetchone()
            if not result:
                return None
            return result[column]
        except Exception as e:
            logger.error(f"Fetch item error: {e}")
            logger.exception(e)
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                self.conn_pool.putconn(conn)

    def fetch_items(self, query: str, column: str, args: List[Any]|None = None, readonly=True)  -> list[Any]:
        """查询并返回指定列的所有值"""
        cursor, conn = None, None
        try:
            cursor, conn = self._execute(query, args, return_dict=True)
            if not readonly:
                conn.commit()

            results = cursor.fetchall()
            if not results:
                return []
            return [result[column] for result in results]
        except Exception as e:
            logger.error(f"Fetch items error: {e}")
            return []
        finally:
            if cursor:
                cursor.close()
            if conn:
                self.conn_pool.putconn(conn)

    def truncate(self, tbl_name: str):
        """清空数据表"""
        self._execute(f'TRUNCATE TABLE "{tbl_name}" RESTART IDENTITY')

    def close(self):
        """关闭数据库连接"""
        if hasattr(self, "conn_pool"):
            self.conn_pool.closeall()
            self.conn_pool = None # type: ignore


pg = Postgres()

__all__ = ["pg"]
