"""Admin API for managing resource whitelists and whitelist items.

This module provides API endpoints for:
1. Searching resources and customers
2. Adding and removing whitelist items
3. Managing customer-specific whitelist items
4. Managing course-specific whitelist items for customers
"""

from blacksheep import Request, json
from loguru import logger

from provision.api.admin_auth import admin_required
from provision.common.router import router
from provision.service.resources import rm
from provision.store.customer import CustomerModel
from provision.store.resource import ResourceModel
from provision.store.resource_whitelist import ResourceWhitelistModel
from provision.store.whitelist_for_all import WhitelistForAllModel


def _update_customer_cache(customer_id: int) -> None:
    """更新客户的资源访问缓存"""
    try:
        rm.update_customer_cache(customer_id)
    except Exception as e:
        logger.error(f"Error updating customer cache: {e}")
        # 如果更新缓存失败，尝试直接更新白名单资源
        try:
            # 获取客户的白名单资源
            whitelist = ResourceWhitelistModel.find_by_customer(customer_id)
            if whitelist and customer_id in rm.customer_resource_id_cache:
                # 更新缓存中的白名单资源
                rm.customer_resource_id_cache[customer_id].update(
                    map(lambda x: x.resource_id, whitelist)
                )
                logger.info(f"Updated whitelist resources for customer {customer_id}")
        except Exception as inner_e:
            logger.error(f"Error updating whitelist resources: {inner_e}")


def _update_all_cache() -> None:
    """更新所有资源访问缓存"""
    try:
        # 更新所有客户的资源访问缓存
        rm.build_cache()

        # 更新公开资源缓存
        from provision.store.whitelist_for_all import WhitelistForAllModel

        # 获取所有公开资源
        public_resources = WhitelistForAllModel.find_all()

        if public_resources:
            public_resource_ids = [
                resource.resource_id for resource in public_resources
            ]
            logger.info(f"Found {len(public_resource_ids)} public resources")

            # 更新所有客户的缓存，添加公开资源
            customers = CustomerModel.find_all()
            for customer in customers:
                if customer.id is not None:
                    if customer.id not in rm.customer_resource_id_cache:
                        rm.customer_resource_id_cache[customer.id] = set()
                    rm.customer_resource_id_cache[customer.id].update(
                        public_resource_ids
                    )
                    logger.debug(f"Added public resources to customer {customer.id}")
    except Exception as e:
        logger.error(f"Error updating all cache: {e}")
        logger.exception(e)


def _delete_whitelist_item(whitelist_item_id: int, customer_id: int) -> bool:
    """删除白名单项并更新缓存"""
    flag = ResourceWhitelistModel.delete_whitelist_item(whitelist_item_id)
    if flag:
        _update_customer_cache(customer_id)

    return flag


@router.get("/api/admin/resource-whitelist/search")
@admin_required
async def search_resources_and_customers(request: Request, customer: str|None = None, resource: str|None = None, course: str|None = None):
    """搜索资源和客户"""
    try:
        # 参数验证（防止前端误传对象）
        if customer is not None and (not isinstance(customer, str) or '[object' in customer):
            logger.warning(f"Invalid customer parameter received: {customer}")
            return json({"error": "Invalid customer parameter"}, status=400)

        customers = []
        resources = []

        # 如果提供了客户账号，进行搜索并返回该客户的授权资源
        if customer is not None and customer.strip():
            # 先尝试精确匹配客户账号
            customer_obj = CustomerModel.find_customer_by_account(customer.strip())
            if customer_obj and customer_obj.id is not None:
                customers = [customer_obj]
            else:
                # 如果精确匹配失败，进行模糊搜索
                customers = CustomerModel.search_by_keyword(customer.strip(), limit=50)

            # 如果找到客户，获取第一个客户的授权资源
            if customers:
                first_customer = customers[0]
                if first_customer.id is not None:
                    # 获取该客户的所有授权资源
                    whitelist_items = ResourceWhitelistModel.find_by_customer(first_customer.id)
                    whitelist_resource_ids = set()
                    if whitelist_items:
                        whitelist_resource_ids = {item.resource_id for item in whitelist_items}

                    # 获取公开资源
                    public_resources = WhitelistForAllModel.find_all()
                    public_resource_ids = set()
                    if public_resources:
                        public_resource_ids = {r.resource_id for r in public_resources}

                    # 合并授权资源和公开资源
                    all_authorized_resource_ids = whitelist_resource_ids | public_resource_ids

                    # 获取资源详情
                    if all_authorized_resource_ids:
                        # 逐个查找资源（因为没有find_by_ids方法）
                        all_resources = []
                        for resource_id in all_authorized_resource_ids:
                            resource_ = ResourceModel.find_by_id(resource_id)
                            if resource_:
                                all_resources.append(resource_)

                        # 按course筛选（如果提供了course参数）
                        if course is not None and course.strip():
                            resources = [r for r in all_resources if r.course == course.strip()]
                        else:
                            resources = all_resources

        # 如果没有提供客户账号，但提供了资源搜索条件
        elif resource is not None:
            resources = ResourceModel.find_all(search_query=resource)

        # 构建响应
        customers_data = []
        for customer_ in customers:
            customers_data.append(customer_.model_dump(exclude={"password", "salt"}))

        # 获取公开资源列表
        public_resources = WhitelistForAllModel.find_all()
        public_resource_ids = (
            [r.resource_id for r in public_resources] if public_resources else []
        )

        # 为每个资源添加是否公开的标志
        resources_data = []
        for resource_ in resources:
            resource_data = resource_.model_dump()
            resource_data["is_public"] = resource_.id in public_resource_ids
            resources_data.append(resource_data)

        return json(
            {
                "customers": customers_data,
                "resources": resources_data,
            }
        )
    except Exception as e:
        logger.error(f"Error searching resources and customers: {e}")
        return json({"error": str(e)}, status=500)


@router.post("/api/admin/resource-whitelist/add")
@admin_required
async def add_whitelist_item(request: Request):
    """添加白名单记录"""
    try:
        data = await request.json()
        customer_id = data.get("customer_id")
        resource_id = data.get("resource_id")
        is_public = data.get("is_public", False)

        if not resource_id:
            return json({"error": "Missing required field: resource_id"}, status=400)

        # 验证资源是否存在
        resource = ResourceModel.find_by_id(resource_id)
        if not resource:
            return json({"error": "Resource not found"}, status=404)

        # 处理公开资源
        if is_public or customer_id == -1:
            logger.info(f"Adding public whitelist record for resource {resource_id}")

            # 导入 WhitelistForAllModel
            from provision.store.whitelist_for_all import WhitelistForAllModel

            # 添加公开白名单记录
            success = WhitelistForAllModel.add_resource(resource_id)

            if success:
                # 更新所有客户的资源访问缓存
                _update_all_cache()
                return json({"success": True, "message": "Resource is now public"})

            return json({"error": "Failed to add public whitelist item"}, status=500)

        # 验证客户是否存在
        if not customer_id:
            return json({"error": "Missing required field: customer_id"}, status=400)

        customer = CustomerModel.find_customer(customer_id)
        if not customer:
            return json({"error": "Customer not found"}, status=404)

        logger.info(
            f"Adding whitelist record for customer {customer_id} and resource {resource_id}"
        )

        # 添加白名单记录
        success = ResourceWhitelistModel.add_whitelist_item(
            customer_id=customer_id, resource_id=resource_id
        )

        if success:
            # 更新客户的资源访问缓存
            _update_customer_cache(customer_id)
            return json({"success": True})

        return json({"error": "Failed to add whitelist item"}, status=500)
    except Exception as e:
        logger.error(f"Error adding whitelist item: {e}")
        return json({"error": str(e)}, status=500)


@router.get("/api/admin/resource-whitelist/customer/{customer_id}")
@admin_required
async def get_customer_whitelist(_: Request, customer_id: int):
    """获取客户的所有白名单项"""
    try:
        # 验证客户是否存在
        customer = CustomerModel.find_customer(customer_id)
        if not customer:
            return json({"error": "Customer not found"}, status=404)

        # 获取客户的所有白名单项
        whitelist_items = ResourceWhitelistModel.find_by_customer(customer_id)

        # 确保 whitelist_items 不为 None
        items_data = []
        if whitelist_items:
            items_data = [item.model_dump() for item in whitelist_items]

        return json(
            {
                "customer": customer.model_dump(exclude={"password", "salt"}),
                "whitelist_items": items_data,
            }
        )
    except Exception as e:
        logger.error(f"Error getting customer whitelist: {e}")
        return json({"error": str(e)}, status=500)


@router.delete(
    "/api/admin/resource-whitelist/customer/{customer_id}/resource/{resource_id}"
)
@admin_required
async def delete_whitelist_item(_: Request, customer_id: int, resource_id: int):
    """删除白名单项"""
    try:
        # 验证客户是否存在
        customer = CustomerModel.find_customer(customer_id)
        if not customer:
            return json({"error": "Customer not found"}, status=404)

        # 验证资源是否存在
        resource = ResourceModel.find_by_id(resource_id)
        if not resource:
            return json({"error": "Resource not found"}, status=404)

        # 查找白名单项
        whitelist_item = ResourceWhitelistModel.find_by_customer_resource(
            customer_id, resource_id
        )
        if not whitelist_item or whitelist_item.id is None:
            return json({"error": "Whitelist item not found"}, status=404)

        # 删除白名单项
        success = _delete_whitelist_item(whitelist_item.id, customer_id)
        if success:
            return json({"success": True})

        return json({"error": "Failed to delete whitelist item"}, status=500)
    except Exception as e:
        logger.error(f"Error deleting whitelist item: {e}")
        return json({"error": str(e)}, status=500)


@router.get("/api/admin/resource-whitelist/customers")
@admin_required
async def get_customers_with_whitelist(_: Request):
    """获取所有客户及其资源白名单信息"""
    try:
        # 获取所有客户
        customers = CustomerModel.find_all()

        # 获取所有资源course
        courses = ResourceModel.get_course()

        # 构建客户资源白名单信息
        result = []
        for customer in customers:
            if customer.id is None:
                continue

            customer_dict = customer.model_dump()

            # 获取客户的资源白名单信息
            whitelist_resources = {}
            for course in courses:
                whitelist_items = ResourceWhitelistModel.find_by_customer_course(
                    customer.id, course
                )
                if whitelist_items:
                    whitelist_resources[course] = {
                        "count": len(whitelist_items),
                        "items": [item.model_dump() for item in whitelist_items],
                    }
                else:
                    whitelist_resources[course] = {"count": 0, "items": []}

            customer_dict["whitelist_resources"] = whitelist_resources
            result.append(customer_dict)

        return json({"customers": result, "courses": courses})
    except Exception as e:
        logger.error(f"Error getting customers with whitelist: {e}")
        return json({"error": str(e)}, status=500)


@router.get("/api/admin/resource-whitelist/customer/{customer_id}/course/{course}")
@admin_required
async def get_customer_course_whitelist(_: Request, customer_id: int, course: str):
    """获取客户在特定course下的资源白名单信息"""
    try:
        # 获取客户信息
        customer = CustomerModel.find_customer(customer_id)
        if not customer:
            return json({"error": "Customer not found"}, status=404)

        # 获取该course下的所有资源
        all_resources = ResourceModel.find_by_course(course)

        # 获取客户在该course下的资源白名单记录
        whitelist_items = ResourceWhitelistModel.find_by_customer_course(
            customer_id, course
        )
        whitelist_resource_ids = set()
        for item in whitelist_items:
            whitelist_resource_ids.add(item.resource_id)

        return json(
            {
                "customer": customer.model_dump(exclude={"password", "salt"}),
                "course": course,
                "all_resources": [resource.model_dump() for resource in all_resources],
                "whitelist_resource_ids": list(whitelist_resource_ids),
            }
        )
    except Exception as e:
        logger.error(
            f"Error getting customer {customer_id} course {course} whitelist: {e}"
        )
        return json({"error": str(e)}, status=500)


@router.post("/api/admin/resource-whitelist/customer/{customer_id}/course/{course}")
@admin_required
async def update_customer_course_whitelist(
    request: Request, customer_id: int, course: str
):
    """更新客户在特定course下的资源白名单信息"""
    try:
        data = await request.json()
        resource_ids = data.get("resource_ids", [])

        # 获取客户信息
        customer = CustomerModel.find_customer(customer_id)
        if not customer:
            return json({"error": "Customer not found"}, status=404)

        # 更新客户在该course下的资源白名单记录
        success = ResourceWhitelistModel.update_customer_course_resources(
            customer_id, course, resource_ids
        )

        if success:
            # 更新客户的资源访问缓存
            _update_customer_cache(customer_id)
            return json({"message": "Resource whitelist updated successfully"})
        else:
            return json({"error": "Failed to update resource whitelist"}, status=500)
    except Exception as e:
        logger.error(
            f"Error updating customer {customer_id} course {course} whitelist: {e}"
        )
        return json({"error": str(e)}, status=500)


@router.get("/api/admin/resource-whitelist/resource/{resource_id}/customers")
@admin_required
async def get_resource_customers(_: Request, resource_id: int):
    """获取资源的授权客户列表"""
    try:
        # 验证资源是否存在
        resource = ResourceModel.find_by_id(resource_id)
        if not resource:
            return json({"error": "Resource not found"}, status=404)


        is_public = WhitelistForAllModel.find_by_resource(resource_id) is not None

        # 获取该资源的所有白名单记录
        whitelist_items = ResourceWhitelistModel.find_by_resource(resource_id)

        # 获取客户信息
        customers = []
        for item in whitelist_items:
            if item.customer_id:
                customer = CustomerModel.find_customer(item.customer_id)
                if customer:
                    customers.append(customer.model_dump(exclude={"password", "salt"}))

        return json(
            {
                "resource": resource.model_dump(),
                "customers": customers,
                "is_public": is_public,
            }
        )
    except Exception as e:
        logger.error(f"Error getting resource customers: {e}")
        return json({"error": str(e)}, status=500)


@router.delete("/api/admin/resource-whitelist/public/{resource_id}")
@admin_required
async def remove_public_whitelist(_: Request, resource_id: int):
    """取消资源的公开状态"""
    try:
        # 验证资源是否存在
        resource = ResourceModel.find_by_id(resource_id)
        if not resource:
            return json({"error": "Resource not found"}, status=404)

        # 检查资源是否是公开的
        public_record = WhitelistForAllModel.find_by_resource(resource_id)
        if not public_record:
            return json({"error": "Resource is not public"}, status=400)

        # 删除公开记录
        success = WhitelistForAllModel.delete_resource(resource_id)
        if not success:
            return json({"error": "Failed to remove public access"}, status=500)

        # 更新所有客户的资源访问缓存
        _update_all_cache()

        return json({"success": True, "message": "Public access removed successfully"})
    except Exception as e:
        logger.error(f"Error removing public access: {e}")
        return json({"error": str(e)}, status=500)
