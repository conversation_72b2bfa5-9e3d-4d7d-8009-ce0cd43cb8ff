import hashlib

import jwt
from blacksheep import Request, json
from loguru import logger
from pydantic import ValidationError

from provision.common.router import router
from provision.common.sessions import sessions
from provision.conf import cfg
from provision.store.customer import CustomerModel
from provision.store.registry import RegistryModel


@router.post("/api/admin/customers")
async def customer_register(request: Request):
    """客户注册"""
    try:
        channel = request.headers.get_first(b"Referer")
        if channel:
            channel = channel.decode()
        else:
            channel = "NA"

        client_ip = request.headers.get_first(b"X-Forwarded-For")
        if not client_ip:
            client_ip = request.headers.get_first(b"X-Real-IP")

        # TODO:
        # 增加对客户端的跟踪和识别，防止多人复用账号

        data = await request.json()
        customer = CustomerModel(**data)

        if customer.account is None or customer.password is None:
            return json({"error": "account or password is empty"}, status=400)

        hashed_pasword, salt = CustomerModel.hash_password(customer.password)
        customer.password = hashed_pasword
        customer.salt = salt

        # 检查账号和手机号是否已存在
        if CustomerModel.check_account_exists(customer.account):
            return json({"error": "Account already exists"}, status=400)

        if CustomerModel.check_phone_exists(customer.phone):
            return json({"error": "Phone number already exists"}, status=400)

        customer.insert_customer()

    except ValidationError as e:
        return json({"error": str(e)}, status=400)
    except Exception as e:
        logger.error(f"Registration error: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.get("/api/admin/customers/{customerId}")
async def get_customer_info(customerId: int):
    """获取客户信息"""
    try:
        return CustomerModel.find_customer(customerId)
    except Exception as e:
        logger.error(f"Error retrieving customer info: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.put("/api/admin/customers/{customerId}")
async def update_customer_info(customerId: int, request: Request):
    """更新客户信息"""
    try:
        data = await request.json()
        password = data.pop("password", "")
        customer = CustomerModel(**data)

        # 检查账号是否存在
        account_check = CustomerModel.find_customer(customerId)
        if account_check is None:
            return json({"error": "Customer not found"}, status=404)

        if password != "":
            hashed_password, salt = CustomerModel.hash_password(password)
            customer.password = hashed_password
            customer.salt = salt

            customer.update_customer(customerId, save_password=True)
        else:
            customer.update_customer(customerId)
        return {"status": "ok", "message": "Customer info updated successfully"}

    except ValidationError as e:
        return json({"error": str(e)}, status=400)
    except Exception as e:
        logger.error(f"Update error: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.delete("/api/admin/customer/deactivate/{account}")
async def deactivate_customer(account: str):
    """注销客户账号（软删除）"""
    try:
        # 使用CustomerModel的deactivate_account方法
        result = CustomerModel.deactivate_account(account)

        if not result:
            return json(
                {"error": "Customer not found or already deactivated"}, status=404
            )

        logger.info(f"Customer deactivated: {account}")
        return {"status": "ok", "message": "Customer deactivated successfully"}

    except Exception as e:
        logger.error(f"Deactivation error: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.get("/api/admin/customers")
async def list_customers(_: Request, search: str | None = None, limit: str | None = None):
    """获取所有客户列表"""
    try:
        if search:
            # 如果有搜索关键字，使用搜索方法
            limit_int = int(limit) if limit and limit.isdigit() else 0
            customers = CustomerModel.search_by_keyword(search, limit_int)
        else:
            # 否则获取所有客户（无分页限制）
            customers = CustomerModel.find_all_customers()

        return json(customers)

    except Exception as e:
        logger.error(f"Error listing customers: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.get("/api/admin/customers/{customer_id}/courses")
async def get_customer_courses(customer_id: int):
    """获取客户的课程列表"""
    try:
        logger.info(f"Getting courses for customer {customer_id}")
        courses = RegistryModel.find_registration_by_customer(
            customer_id, include_expired=False, include_inactive=False
        )
        logger.info(
            f"Found {len(courses)} active and non-expired courses for customer {customer_id}"
        )
        return json(courses)

    except Exception as e:
        logger.error(f"Error retrieving customer courses: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.post("/api/academy/customer/change-password")
async def change_password(request: Request):
    """修改用户密码"""
    try:
        data = await request.json()
        customer_id = data.get("customer_id")
        current_password = data.get("current_password")
        new_password = data.get("new_password")

        if not customer_id or not current_password or not new_password:
            return json({"error": "Missing required fields"}, status=400)

        # 验证用户身份
        token = request.cookies.get("qsession-token")
        if not token:
            return json({"error": "Unauthorized"}, status=401)

        try:
            token_data = jwt.decode(
                token, cfg.provision.server.secret, algorithms=["HS256"]
            )
            token_customer_id = token_data.get("account_id")

            # 确保令牌中的用户ID与请求中的用户ID匹配
            if str(token_customer_id) != str(customer_id):
                logger.warning(
                    f"Token customer ID {token_customer_id} does not match request customer ID {customer_id}"
                )
                return json({"error": "Unauthorized"}, status=401)

            # 确保会话有效
            if not sessions.is_active(token_customer_id):
                return json({"error": "Session expired"}, status=401)
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid token during password change: {e}")
            return json({"error": "Invalid token"}, status=401)

        # 获取用户信息
        customer = CustomerModel.find_customer(customer_id)
        if not customer:
            return json({"error": "Customer not found"}, status=404)

        # 验证当前密码
        salt = customer.salt if customer.salt else ""

        hash_current_password, salt = CustomerModel.hash_password(
            current_password, salt
        )

        if hash_current_password != customer.password:
            return json({"error": "Current password is incorrect"}, status=400)

        # 更新密码
        result = customer.update_password(new_password)
        if result:
            return json(
                {"status": "success", "message": "Password updated successfully"}
            )
        else:
            return json({"error": "Failed to update password"}, status=500)

    except Exception as e:
        logger.error(f"Error changing password: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.get("/api/customers/<int:customer_id>/courses")
async def get_customer_courses_public(customer_id: int):
    """Get customer's registered courses with combined data"""
    try:
        # 1. Get raw registration data from database (only active and non-expired courses)
        registrations = RegistryModel.find_registration_by_customer(
            customer_id, include_expired=False, include_inactive=False
        )

        # 3. Combine data sources
        enhanced_courses = []
        for reg in registrations:
            course_id = reg["course"]
            config = cfg.get_resources_config(course_id)

            # Generate chap array
            chap_start = config.get("start", 1)
            chap_end = config.get("end", 1)
            chap = [str(i).zfill(2) for i in range(chap_start, chap_end + 1)]

            enhanced_courses.append(
                {
                    "id": reg["id"],
                    "course_id": course_id,
                    "title": config.get("title", course_id.upper()),
                    "plan": reg["plan"],
                    "cover": config.get("cover", ""),
                    "chap": chap,
                    "start_time": reg["start_time"].isoformat(),
                    "prime_end": reg["prime_end"].isoformat(),
                    "expire_time": reg["expire_time"].isoformat(),
                }
            )

        return json(enhanced_courses)

    except Exception as e:
        logger.error(f"Error getting courses: {e}")
        return json({"error": "Internal server error"}, status=500)
