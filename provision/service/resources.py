"""
资源访问控制模块，用于处理资源访问权限的共享功能
"""

import datetime
from collections import defaultdict
from typing import Any, Dict, Optional, Set
from urllib.parse import parse_qs, urlparse

from loguru import logger

from provision.common.helper import get_meta_from_notebook
from provision.conf import cfg
from provision.conf.plans import sp
from provision.store.registry import RegistryModel
from provision.store.resource import ResourceModel
from provision.store.resource_whitelist import ResourceWhitelistModel
from provision.store.whitelist_for_all import WhitelistForAllModel


class ResourceManager:
    """课程资源管理器，用于管理课程资源编号、访问权限cache等。"""

    def __init__(self):
        """初始化资源访问管理器"""

        # 系统中受控的资源列表
        self.controlled_resources: set[int] = set()

        # 用来将路径转换为资源id
        # {course}/{rel_path} -> resource_id
        self.path_map = {}

        # 用来决定客户能否访问某个 resource： customer_id -> set(resource_id)
        self.customer_resource_id_cache: Dict[int, Set[int]] = defaultdict(set)

        # 所有人可访问的资源ID集合
        self.public_resources: Set[int] = set()

    def on_start(self):
        """在程序启动时执行的初始化任务"""
        self.update_resources()
        self.build_path_map()
        self.load_public_resources()
        self.build_cache()

    def load_public_resources(self):
        """加载所有人可访问的资源ID"""
        try:
            public_resources = WhitelistForAllModel.find_all()
            self.public_resources = set(item.resource_id for item in public_resources)
            logger.info(f"Loaded {len(self.public_resources)} public resources")
        except Exception as e:
            logger.error(f"Error loading public resources: {e}")
            self.public_resources = set()

    def update_resources(self):
        """在启动时更新资源信息，也相当于初始化

        对所有资源都要提取信息和编号，无论它们对某个用户能否访问。
        """
        logger.info("开始更新资源信息...")
        # 对所有在硬盘上的资源，提取信息并入库
        total_processed = 0
        for course, sub, rel_paths in sp.iter_course_disk_resources():
            logger.info(f"处理课程 {course}/{sub}，共 {len(rel_paths)} 个文件")
            home = cfg.get_course_home(course)
            division = cfg.get_course_division(course)
            if home is None:
                logger.error(f"Home path not found for course {course}")
                continue
            for i, item in enumerate(rel_paths):
                path = home / item
                logger.debug(f"处理文件 {path}")
                try:
                    title, description, price, publish_date, img = get_meta_from_notebook(
                        path
                    )
                    resource = ResourceModel(
                        course=course,
                        resource=sub,
                        seq=i + 1,
                        title=title,
                        description=description,
                        rel_path=item,
                        publish_date=publish_date,
                        price=price,
                        division=division,
                        img=img,
                    )
                    resource.save()
                    total_processed += 1
                    if total_processed % 10 == 0:
                        logger.info(f"已处理 {total_processed} 个文件")
                except Exception as e:
                    logger.error(f"处理文件 {path} 时出错: {e}")
                    logger.exception(e)
        logger.info(f"资源信息更新完成，共处理 {total_processed} 个文件")

        # 对所有在库的资源，如果不能在本地找到，则删除
        flag = False
        for resource in ResourceModel.find_all():
            if resource.id is None:
                continue

            home = cfg.get_course_home(resource.course)
            if home is None:
                flag = self.delete_resource(resource.id, False) or flag
                continue
            path = home / resource.rel_path
            if not path.exists():
                # 数据库查出来的记录，一定有id
                flag = self.delete_resource(resource.id, False) or flag

        if flag:
            self.build_cache()

    def delete_resource(self, resource_id: int, rebuild_cache: bool = True) -> bool:
        """删除资源

        Args:
            resource_id: 要删除的资源ID
            rebuild_cache: 是否重建缓存，默认为True

        Returns:
            bool: 删除是否成功
        """
        try:
            # 先删除资源白名单中的相关记录
            whitelist_items = ResourceWhitelistModel.find_by_resource(resource_id)

            # 获取受影响的客户ID列表，用于后续更新特定客户的缓存
            affected_customers = set()
            for item in whitelist_items:
                if item.customer_id:
                    affected_customers.add(item.customer_id)

                # 删除白名单记录
                if item.id:
                    ResourceWhitelistModel.delete_whitelist_item(item.id)

            # 删除资源本身
            flag = ResourceModel.delete(resource_id)

            # 更新缓存
            if flag and rebuild_cache:
                if affected_customers:
                    # 如果有受影响的客户，只更新这些客户的缓存
                    for customer_id in affected_customers:
                        self.update_customer_cache(customer_id)
                else:
                    # 如果没有受影响的客户，或者无法确定，则重建整个缓存
                    self.build_cache()

            return flag
        except Exception as e:
            logger.error(f"Error deleting resource {resource_id}: {e}")
            return False

    def build_path_map(self):
        """从{course}/{relpath} -> id的map，以及获得所有的受控资源id"""
        legacy = self.path_map.copy()

        try:
            recs = ResourceModel.find_all()

            self.path_map = {}
            for rec in recs:
                self.path_map[f"{rec.course}/{rec.rel_path}"] = rec.id

            self.controlled_resources = set(self.path_map.values())
        except Exception as e:
            logger.error("Failed to build path map: {}, revert back", e)
            self.path_map = legacy

    def parse_url_path(self, url_path: str) -> tuple[str, str, str, str, str]:
        """解析URL路径，提取课程、账号、资源路径等信息

        Args:
            url_path: URL路径，如 "/course/fa/preview/lab/tree/courseware/02.ipynb"

        Returns:
            tuple: (course, account, rel_path, resource_type, prefix)
        """
        # 移除开头的斜杠并分割路径
        parts = url_path.strip("/").split("/")

        if len(parts) < 6 or parts[0] != "course":
            raise ValueError(f"Invalid URL path format: {url_path}")

        course = parts[1]  # fa
        account = parts[2]  # preview
        prefix = "/".join(parts[3:5])  # lab/tree 或 api/contents
        rel_path = "/".join(parts[5:])  # courseware/02.ipynb

        # 提取资源类型（第一级目录）
        resource_type = parts[5] if len(parts) > 5 else ""

        return course, account, rel_path, resource_type, prefix

    def map_path_to_article_id(self, url_path: str) -> Optional[int]:
        """将URL路径映射到文章ID"""
        try:
            course, account, rel_path, resource_type, prefix = self.parse_url_path(
                url_path
            )
        except ValueError:
            return None

        key = f"{course}/{rel_path}"
        return self.path_map.get(key, None)

    def get_article_id_from_url(self, url: str) -> Optional[int]:
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)

        # 优先从查询参数中获取 articleId
        if "articleId" in query_params:
            try:
                return int(query_params["articleId"][0])
            except (ValueError, IndexError):
                return None
        return None

    def filter_plan_exclude(
        self, resources: list[ResourceModel], course: str, plan: str, resource_type: str
    ) -> list[int]:
        available = []
        for item in resources:
            if not sp.is_exclude(course, plan, resource_type, item.rel_path):
                available.append(item.id)

        return available

    def calc_accessible_resources(
        self,
        course: str,
        plan: str,
        start: datetime.datetime,
        refund_end: datetime.datetime,
        now: datetime.datetime,
    ) -> list[int]:
        """根据套餐和订阅时间计算可访问资源

        被resource_manager调用，以构建客户资源访问列表的 cache。只筛选出可以访问的资源。
        """
        accessible = []
        plan_model = sp.plans.get(course)
        assert plan_model
        initial = plan_model.initial
        release_every = plan_model.release_every

        division = cfg.get_course_division(course)
        order_ascend = division != "blog"
        for sub in cfg.get_course_resources(course):
            sorted_resources = ResourceModel.find_course_sub_resources(
                course, sub, order_ascend
            )
            if sorted_resources is None:
                continue

            # 对于blog课程，需要特别处理
            if course == "blog":
                # 1. 先添加所有price_tier_id为null的公开资源
                public_resources = []
                for resource in sorted_resources:
                    if resource.price_tier_id is None and resource.id is not None:
                        public_resources.append(resource.id)
                        accessible.append(resource.id)

                # 2. 获取用户已授权的资源（白名单）
                authorized_resources = set()
                # 注意：这里不能直接查询白名单，因为我们在构建缓存过程中
                # 白名单会在build_cache方法的后续步骤中添加

                # 3. 对于付费资源，按进度控制处理，但要排除已经允许访问的资源
                paid_resources = [r for r in sorted_resources
                                if r.price_tier_id is not None and r.id not in public_resources]

                plan_available = self.filter_plan_exclude(
                    paid_resources, course, plan, sub
                )
                progress = self.calc_progress(
                    plan_available, now, start, refund_end, initial, release_every
                )
                accessible.extend(plan_available[:progress])
            else:
                # 非blog课程，使用原有逻辑
                plan_available = self.filter_plan_exclude(
                    sorted_resources, course, plan, sub
                )
                progress = self.calc_progress(
                    plan_available, now, start, refund_end, initial, release_every
                )
                accessible.extend(plan_available[:progress])

        return accessible

    def calc_progress(
        self,
        available: list[Any],
        now: datetime.datetime,
        start: datetime.datetime,
        refund_end: datetime.datetime,
        initial: int,
        release_every: float,
    ) -> float:
        """根据客户注册课程的时间，计算进度"""
        if now < refund_end:
            # 在退款期内， 根据公式计算可访问资源数量
            time_diff = (now - start).total_seconds() / 86400
            progress = min(
                initial + int(time_diff / release_every),
                len(available),
            )
        else:
            # 退款期后，可以访问所有套餐资源
            progress = len(available)

        return progress

    def calc_resource_unlock_time(
        self,
        resource_index: int,
        start_time: datetime.datetime,
        refund_end: datetime.datetime,
        initial: int,
        release_every: float,
    ) -> datetime.datetime:
        """计算第 i 个资源的准确解锁时间

        Args:
            resource_index: 资源索引（从1开始）
            start_time: 订阅开始时间
            refund_end: 退款期结束时间
            initial: 初始可访问资源数量
            release_every: 每隔多少天释放一个资源

        Returns:
            datetime.datetime: 资源解锁时间
        """
        if resource_index <= initial:
            # 初始资源，立即可访问
            return start_time

        # 计算需要等待的天数
        days_to_wait = (resource_index - initial) * release_every
        unlock_time = start_time + datetime.timedelta(days=days_to_wait)

        # 如果计算出的解锁时间超过了退款期，则在退款期结束时解锁
        if unlock_time > refund_end:
            return refund_end

        return unlock_time

    def update_customer_cache(self, customer_id: int) -> None:
        """更新单个客户的资源访问缓存

        Args:
            customer_id: 客户ID
        """
        # 获取客户的所有有效注册记录
        registries = RegistryModel.find_active_registries()
        # 过滤出当前客户的记录
        registries = [r for r in registries if r.customer_id == customer_id]

        # 清空客户的资源访问缓存
        if customer_id in self.customer_resource_id_cache:
            self.customer_resource_id_cache[customer_id] = set()

        # 处理每个注册记录
        for registry in registries:
            if not registry.course or not registry.plan:
                continue

            # 计算客户可以访问的资源
            now = datetime.datetime.now(cfg.get_timezone())
            accessible = self.calc_accessible_resources(
                registry.course,
                registry.plan,
                registry.start_time,
                registry.refund_end,
                now,
            )

            # 更新缓存
            self.customer_resource_id_cache[customer_id].update(accessible)

        # 增加白名单资源
        whitelist = ResourceWhitelistModel.find_by_customer(customer_id)
        if whitelist is not None:
            self.customer_resource_id_cache[customer_id].update(
                map(lambda x: x.resource_id, whitelist)
            )

        # 增加对所有人开放的资源
        public_resources = WhitelistForAllModel.find_all()
        if public_resources:
            self.customer_resource_id_cache[customer_id].update(
                [resource.resource_id for resource in public_resources]
            )

    def build_cache(self):
        """在启动时（或者指定时间）构建资源访问缓存，即 customer_resouce_id_cache"""
        legacy = self.customer_resource_id_cache.copy()
        self.customer_resource_id_cache = defaultdict(set)
        try:
            for registra in RegistryModel.find_active_registries():
                customer = registra.customer_id
                course = registra.course
                plan = registra.plan
                start = registra.start_time
                refund_end = registra.refund_end

                now = datetime.datetime.now(cfg.get_timezone())
                accessible = self.calc_accessible_resources(
                    course, plan, start, refund_end, now
                )

                self.customer_resource_id_cache[customer].update(accessible)

                # 增加白名单资源
                whitelist = ResourceWhitelistModel.find_by_customer(customer)
                if whitelist is not None:
                    self.customer_resource_id_cache[customer].update(
                        map(lambda x: x.resource_id, whitelist)
                    )

            # 为所有客户添加blog免费资源(price_tier_id为null)
            blog_free_resources = set()
            try:
                blog_resources = ResourceModel.find_by_course("blog")
                for resource in blog_resources:
                    if resource.price_tier_id is None and resource.id is not None:
                        blog_free_resources.add(resource.id)
            except Exception as e:
                logger.error(f"Error loading blog free resources: {e}")

            # 获取所有客户（包括有注册记录的和可能通过其他方式访问的）
            all_customers = set(self.customer_resource_id_cache.keys())

            # 为每个客户添加blog免费资源和公开资源
            for customer in all_customers:
                if blog_free_resources:
                    self.customer_resource_id_cache[customer].update(blog_free_resources)
                if self.public_resources:
                    self.customer_resource_id_cache[customer].update(self.public_resources)
        except Exception as e:
            logger.error("Failed to build cache: {}, revert back", e)
            self.customer_resource_id_cache = legacy

    def label_course_resources(
        self,
        course: str,
        sub: str,
        plan: str,
        start: datetime.datetime,
        refund_end: datetime.datetime,
        now: datetime.datetime,
        url_prefix: str,
    ):
        """
        为指定课程的指定资源类型添加标签，供academy使用

        Returns:
            [{
                chapter: seq,
                type: resource_type
                rel_path: rel_path,
                reason: "" or 未授权原因,
                "resourceId": id of resource,
                url: url
            }]
        """
        labels = []
        plan_model = sp.plans.get(course)
        assert plan_model
        initial = plan_model.initial
        release_every = plan_model.release_every

        all_resources = ResourceModel.find_course_sub_resources(course, sub)
        if all_resources is None:
            return []

        url_prefix = url_prefix[:-1] if url_prefix.endswith("/") else url_prefix
        for i, resource in enumerate(all_resources, start=1):
            url = f"{url_prefix}/{resource.rel_path}?articleId={resource.id}"

            chapter = f"{resource.seq:02d}"
            reason = None
            if sp.is_exclude(course, plan, sub, resource.rel_path):
                reason = "升级套餐 解锁本章"
            else:
                # 计算该资源的准确解锁时间
                unlock_time = self.calc_resource_unlock_time(
                    i, start, refund_end, initial, release_every
                )
                # 如果资源还未解锁，显示解锁时间
                if now < unlock_time:
                    reason = f"将于{unlock_time.strftime('%-m月%-d日%-H时')}解锁"

            labels.append(
                {
                    "chapter": chapter,
                    "type": sub,
                    "reason": reason,
                    "resourceId": resource.id,
                    "url": None if reason else url,
                }
            )

        return labels

    def label_course_resources_for_preview(
        self,
        course: str,
        sub: str,
        url_prefix: str,
    ):
        """
        为preview账号的指定课程资源添加标签

        Preview账号的特殊处理逻辑：
        - 前initial个资源完全开放
        - 其他资源显示"购买后解锁"

        Returns:
            [{
                chapter: seq,
                type: resource_type
                reason: "" or 未授权原因,
                "resourceId": id of resource,
                url: url
            }]
        """
        labels = []
        plan_model = sp.plans.get(course)
        assert plan_model
        initial = plan_model.initial

        all_resources = ResourceModel.find_course_sub_resources(course, sub)
        if all_resources is None:
            return []

        url_prefix = url_prefix[:-1] if url_prefix.endswith("/") else url_prefix
        for i, resource in enumerate(all_resources, start=1):
            url = f"{url_prefix}/{resource.rel_path}?articleId={resource.id}"

            chapter = f"{resource.seq:02d}"
            reason = None

            # Preview账号逻辑：前initial个资源开放，其他显示"购买后解锁"
            if i > initial:
                reason = "购买后解锁"

            labels.append(
                {
                    "chapter": chapter,
                    "type": sub,
                    "reason": reason,
                    "resourceId": resource.id,
                    "url": None if reason else url,
                }
            )

        return labels

    def allow_access(self, url: str, customer_id: int) -> bool:
        """Check if a customer has access to a URL

        Args:
            url: 请求的URL路径
            customer_id: 客户ID

        Returns:
            bool: 是否允许访问
        """
        # 解析URL路径
        article_id = self.get_article_id_from_url(url)
        if article_id is not None:
            # 检查是否是公开资源
            if article_id in self.public_resources:
                return True
            # 检查客户是否有访问权限
            return article_id in self.customer_resource_id_cache.get(customer_id, set())

        parsed_url = urlparse(url)
        article_id = self.map_path_to_article_id(parsed_url.path)
        if article_id is None:
            # 非受控资源，允许
            return True
        else:
            # 检查是否是公开资源
            if article_id in self.public_resources:
                return True
            # 检查客户是否有访问权限
            return article_id in self.customer_resource_id_cache.get(customer_id, set())

    def make_resource_public(self, resource_id: int) -> bool:
        """
        将资源设为公开，允许所有人访问

        Args:
            resource_id: 资源ID

        Returns:
            bool: 是否成功
        """
        try:
            # 获取资源信息
            resource = ResourceModel.find_by_id(resource_id)
            if not resource:
                logger.error(f"Resource {resource_id} not found")
                return False

            # 添加到数据库
            success = WhitelistForAllModel.add_resource(resource_id)
            if not success:
                return False

            # 更新内存中的公开资源集合
            self.public_resources.add(resource_id)

            # 重建缓存，确保所有人能立即访问
            self.build_cache()

            logger.info(f"Resource {resource_id} is now public")
            return True

        except Exception as e:
            logger.error(f"Error making resource public: {e}")
            return False

    def add_resource(
        self,
        notebook_path: str,
        course: str,
        resource_type: str,
        seq: int | None = None,
        allow_all: bool = False,
    ) -> Optional[int]:
        """
        添加单个资源

        Args:
            notebook_path: notebook 文件路径，相对于课程根目录
            course: 课程ID
            resource_type: 资源类型，如 courseware, articles 等
            seq: 资源序号，如果为 None，则自动计算
            allow_all: 是否允许所有人访问

        Returns:
            int: 资源ID，如果添加失败则返回 None
        """
        try:
            # 获取课程根目录
            home = cfg.get_course_home(course)
            if home is None:
                logger.error(f"Home path not found for course {course}")
                return None

            # 构建完整路径
            path = home / notebook_path
            if not path.exists():
                logger.error(f"Notebook not found: {path}")
                return None

            # 获取课程门类
            division = cfg.get_course_division(course)
            if division is None:
                logger.error(f"Division not found for course {course}")
                return None

            # 从 notebook 中提取 meta 信息
            title, description, price, publish_date, img = get_meta_from_notebook(path)

            # 如果没有指定序号，则自动计算
            if seq is None:
                # 获取该课程该资源类型的最大序号
                resources = ResourceModel.find_course_sub_resources(
                    course, resource_type
                )
                if resources:
                    seq = max(r.seq for r in resources) + 1
                else:
                    seq = 1

            # 创建资源
            resource = ResourceModel(
                course=course,
                resource=resource_type,
                seq=seq,
                title=title,
                description=description,
                rel_path=notebook_path,
                publish_date=publish_date,
                price=price,
                division=division,
                img=img,
            )

            # 保存资源
            resource_id = resource.save()

            # 更新路径映射
            self.path_map[f"{course}/{notebook_path}"] = resource_id
            self.controlled_resources.add(resource_id)

            # 如果允许所有人访问，则添加到公开资源列表
            if allow_all:
                # 添加到数据库
                WhitelistForAllModel.add_resource(resource_id)

                # 更新内存中的公开资源集合
                self.public_resources.add(resource_id)

                # 重建缓存，确保所有人能立即访问
                self.build_cache()

            logger.info(f"Resource added successfully: {resource_id}")
            return resource_id

        except Exception as e:
            logger.error(f"Error adding resource: {e}")
            return None


# 创建全局实例
rm = ResourceManager()
