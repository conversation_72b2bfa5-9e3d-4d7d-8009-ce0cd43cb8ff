"""

## Example of shell_execute_expect
cmd = "docker exec -it ols /usr/local/lsws/admin/misc/admpass.sh"
prompts = [
    "User name.*",
    "Password.*",
    "Retype password.*"
]
inputs = ["admin", "123456", "123456"]
shell_execute_expect(cmd, prompts, inputs)
"""

import datetime
import json
import os
import queue
import random
import shlex
import subprocess
import sys
from importlib.metadata import version
from pathlib import Path
from subprocess import PIPE, STDOUT, Popen
from typing import Any, Optional, Tuple, Union

import arrow
import nbformat
import pexpect
from loguru import logger
from rich.console import Console

try:
    provision_version = version("provision")
except Exception:
    provision_version = "dev"

from provision.conf import cfg


def warn(msg: str):
    """警告用户"""
    console = Console()
    console.print(f"[bold red]Warning[/bold red]: {msg}")


def attention(msg: str):
    """提示用户"""
    console = Console()
    console.print(f"[bold cyan]Attention[/bold cyan]: {msg}")


def succeed(msg: str):
    """成功完成提示"""
    console = Console()
    console.print(f"[bold green]Success[/bold green]: {msg}")


def info(msg: str):
    """一般性提示"""
    console = Console()
    console.print(f"[bold green]Info[/bold green]: {msg}")

def is_root():
    return os.geteuid() == 0

def shell_execute(
    cmd: str, output: Any = sys.stdout, dry_run=False, shell=False
) -> str:
    """执行shell命令并打印输出。

    Args:
        cmd: 待执行的命令
        output: 默认打印到控制台。通过改变output，可以重定向到文件或者队列。
        dry_run: 是否仅打印命令，不执行。
        shell: 是否使用shell执行命令。默认为False，表示使用exec执行命令。
    """
    if dry_run:
        return ""

    args = shlex.split(cmd)
    results = []
    with Popen(
        args, stdin=PIPE, stdout=PIPE, stderr=STDOUT, text=True, shell=shell
    ) as proc:
        for line in proc.stdout:  # type: ignore
            if output is None:
                results.append(line)
                continue

            if isinstance(output, queue.Queue):
                output.put(line)
            else:
                print(line, file=output)

            results.append(line)

    return "\n".join(results)

def run_with_sudo(cmd: str, dry_run=False
):
    """执行shell命令并打印输出。

    Args:
        cmd: 待执行的命令
        output: 默认打印到控制台。通过改变output，可以重定向到文件或者队列。
        dry_run: 是否仅打印命令，不执行。
    """
    if dry_run:
        return ""

    args = shlex.split(cmd)
    if not is_root():
        args.insert(0, "sudo")

    try:
        result = subprocess.run(args, check=True, text=True, capture_output=True)
        succeed(f"成功执行命令：{cmd}, {result.stdout}")
        return result.stdout
    except subprocess.CalledProcessError:
        warn(f"命令执行失败：{cmd}")
        return ""


def append_file(file_path: str, content: str) -> None:
    """向文本文件追加内容

    自动检测文件是否以换行符结尾，并在写入后，自动加入换行符。

    Args:
        file_path: 文件路径
        content: 待追加的内容
    """
    path = Path(file_path).expanduser()
    with open(path, "r", encoding="utf-8") as f:
        lines = f.readlines()
        if not lines[-1].endswith("\n"):
            lines[-1] = lines[-1] + "\n"
        lines.append(content)
        lines.append("\n")

    with open(path, "w", encoding="utf-8") as f:
        f.writelines(lines)


def shell_execute_expect(cmd: str, prompts: list[str], inputs: list[str]) -> None:
    """执行shell命令并打印输出。

    Args:
        cmd: 待执行的命令
    """
    child = pexpect.spawn(cmd)
    child.logfile = open("/tmp/shell_execute.log", "wb")

    for prompt, input_ in zip(prompts, inputs):
        child.expect(prompt)
        child.sendline(input_)


def set_env_variable(key, value):
    """向 ~/.bash_profile 文件中追加或修改环境变量

    Args:
        key: 环境变量的键
        value: 环境变量的值
    """
    bash_profile_path = Path("~/.bash_profile").expanduser()

    # 如果 .bash_profile 不存在，检查并复制 .bashrc 的内容
    if not bash_profile_path.exists():
        bashrc_path = Path("~/.bashrc").expanduser()
        initial_content = []

        # 如果 .bashrc 存在，复制其内容
        if bashrc_path.exists():
            with open(bashrc_path, "r", encoding="utf-8") as f:
                initial_content = f.readlines()

        # 添加基础配置
        if not initial_content:
            initial_content = [
                "# .bash_profile\n",
                "\n",
                "# Get the aliases and functions from .bashrc\n",
                "if [ -f ~/.bashrc ]; then\n",
                "    . ~/.bashrc\n",
                "fi\n",
                "\n",
                "# User specific environment and startup programs\n",
                "\n",
            ]

        # 创建 .bash_profile 并写入初始内容
        with open(bash_profile_path, "w", encoding="utf-8") as f:
            f.writelines(initial_content)

    with open(bash_profile_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    # 检查环境变量是否已经存在
    found = False
    for i, line in enumerate(lines):
        if line.startswith(f"export {key}="):
            found = True
            existing_value = line.split("=")[1].strip().strip('"')
            if existing_value == value:
                print(f"环境变量 {key} 已经存在且值相同，不做改动。")
            else:
                lines[i] = f'export {key}="{value}"\n'
                print(f"环境变量 {key} 已更新为 {value}。")
            break

    if not found:
        # 如果环境变量不存在，追加该环境变量
        lines.append(f'export {key}="{value}"\n')
        print(f"环境变量 {key} 已添加，值为 {value}。")

    with open(bash_profile_path, "w", encoding="utf-8") as f:
        f.writelines(lines)


def get_env_value(key: str) -> str:
    """获取环境变量的值

    此方法可在设置环境变量之后，没有重启shell时使用
    """
    path = Path("~/.bash_profile").expanduser()
    if not path.exists():
        return ""

    try:
        with open(path, "r", encoding="utf-8") as file:
            for line in file:
                if line.startswith("export "):
                    parts = line.strip().split("=", 1)
                    if len(parts) == 2:
                        var = parts[0].replace("export ", "")
                        if var == key:
                            return parts[1]
    except Exception:
        pass
    return ""


def get_meta_from_notebook(
    notebook_path: Path,
) -> Tuple[str, str, float, datetime.datetime, str]:
    try:
        logger.debug(f"正在读取notebook文件: {notebook_path}")
        nb = nbformat.read(notebook_path, as_version=4)

        metadata = nb.get("metadata", {})

        title = metadata.get("title", notebook_path.stem)
        description = metadata.get("excerpt", "")
        date_or_str = metadata.get("date", arrow.now("Asia/Shanghai"))
        publish_date = arrow.get(date_or_str, tzinfo="Asia/Shanghai").datetime
        price = metadata.get("price", 0.0)
        img = metadata.get("img", "")  # 提取 notebook 中的 img 属性

        return title, description, price, publish_date, img
    except Exception as e:
        logger.error(f"读取notebook文件失败: {notebook_path}, 错误: {e}")
        # 返回默认值，避免程序崩溃
        return (
            notebook_path.stem,  # title
            "",  # description
            0.0,  # price
            arrow.now("Asia/Shanghai").datetime,  # publish_date
            ""  # img
        )


def update_notebook_metadata(
    notebook_path: Path,
    title: Optional[str] = None,
    description: Optional[str] = None,
    price: Optional[float] = None,
    publish_date: Optional[datetime.datetime] = None,
    img: Optional[str] = None,
) -> bool:
    """更新 notebook 的 metadata

    Args:
        notebook_path: notebook 文件路径
        title: 标题
        description: 描述
        price: 价格
        publish_date: 发布日期
        img: 图片 URL

    Returns:
        bool: 更新是否成功
    """
    try:
        # 读取 notebook
        nb = nbformat.read(notebook_path, as_version=4)

        # 获取当前 metadata
        metadata = nb.get("metadata", {})

        # 更新 metadata
        if title is not None:
            metadata["title"] = title
        if description is not None:
            metadata["excerpt"] = description
        if price is not None:
            metadata["price"] = price
        if publish_date is not None:
            metadata["date"] = publish_date.strftime("%Y-%m-%d %H:%M:%S")
        if img is not None:
            metadata["img"] = img

        # 设置更新后的 metadata
        nb["metadata"] = metadata

        # 写入 notebook
        nbformat.write(nb, notebook_path)

        return True
    except Exception as e:
        logger.error(f"Error updating notebook metadata: {e}")
        return False


def random_image():
    """从本地 meta.json 文件获取随机图片URL"""
    deck = getattr(random_image, "deck", [])
    if deck is None or len(deck) == 0:
        try:
            gallery_path = Path(__file__).parent.parent / "conf" / "gallery.json"
            if gallery_path.exists():
                with open(gallery_path, "r", encoding="utf-8") as f:
                    deck = json.load(f)
                    random.shuffle(deck)
                    setattr(random_image, "deck", deck)
            else:
                logger.error(f"Meta file not found: {gallery_path}")
        except Exception as e:
            logger.error(f"Error getting random image: {e}")
            deck = []

    if len(deck) > 0:
        filename = deck.pop()
        logger.info(filename)
        return f"https://images.jieyu.ai/images/hot/gallery/4x3/{filename}"
    else:
        return ""


def backup_database(backup_file: Union[str, Path]|None = None) -> Path:
    """
    备份 PostgreSQL 数据库

    使用 Docker 容器中的 pg_dump 命令备份数据库到指定文件。如果未指定文件，则使用默认路径。

    Args:
        backup_file: 备份文件路径，如果为 None，则使用默认路径

    Returns:
        备份文件的路径

    Raises:
        Exception: 备份失败时抛出异常
    """
    # 如果未指定备份文件，则使用默认路径
    if backup_file is None:
        # 获取当前时间作为备份文件名
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H%M%S")
        home_dir = Path.home()
        backup_base = home_dir / ".backups" / "provision"
        os.makedirs(backup_base, exist_ok=True)
        backup_file = backup_base / f"provision_{timestamp}.sql"
    else:
        backup_file = Path(backup_file)
        os.makedirs(backup_file.parent, exist_ok=True)

    logger.info(f"Backing up database to: {backup_file}")

    try:
        # 确保配置已加载
        if not hasattr(cfg, 'provision') or cfg.provision is None:
            logger.info("Configuration not loaded, attempting to load...")
            cfg.load_config()

        # 从配置中获取数据库连接信息
        try:
            # 从 provision.yaml 中获取 PostgreSQL 连接信息
            db_user = cfg.provision.postgres.user
            db_password = cfg.provision.postgres.password
            db_name = cfg.provision.postgres.database
            logger.info(f"Using database config: user={db_user}, database={db_name}")
        except (AttributeError, TypeError) as e:
            # 如果配置中没有 PostgreSQL 连接信息，则使用环境变量或默认值
            logger.warning(f"Failed to get database config from provision.yaml: {e}")
            db_name = os.environ.get("POSTGRES_DB", "provision")
            db_user = os.environ.get("POSTGRES_USER", "postgres")
            db_password = os.environ.get("POSTGRES_PASSWORD", "")
            logger.info(f"Using environment variables: user={db_user}, database={db_name}")

        # 使用 Docker 容器中的 pg_dump 命令备份数据库
        # 容器名为 postgres，在容器内部连接到 localhost
        docker_cmd = [
            "docker", "exec", "-e", f"PGPASSWORD={db_password}",
            "postgres", "pg_dump",
            "-h", "localhost",
            "-p", "5432",
            "-U", db_user,
            "-d", db_name
        ]

        # 执行备份命令并将输出重定向到文件
        with open(backup_file, 'w') as f:
            result = subprocess.run(docker_cmd, stdout=f, stderr=subprocess.PIPE, text=True)

        if result.returncode != 0:
            raise Exception(f"Database backup failed: {result.stderr}")

        logger.info(f"Database backup completed successfully: {backup_file}")
        return backup_file

    except Exception as e:
        logger.error(f"Failed to backup database: {e}")
        raise
